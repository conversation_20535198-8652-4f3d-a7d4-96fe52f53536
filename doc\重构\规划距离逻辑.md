# 旅行规划中的时空连续性逻辑 (V3.1 - 动态感知版)

本文档详细说明了 AutoPilot AI 项目在V3统一架构下，如何通过**“迭代式上下文规划”（ICP）**模型，从根本上保证行程规划在**时间**和**空间**两个维度上的连续性与合理性。

## 1. 核心思想：从“静态后排序”到“动态预计算”

旧版架构采用的是一种**“静态地理排序”**的后处理方法。V3.0初步改进为时序规划，但仍有不足。

**V3.1架构将时空逻辑提升为一种“动态预计算”机制，使其成为AI决策的核心依据。**

其核心思想是：**在AI思考“下一步去哪”的每一刻，都为其提供一个基于当前真实地理位置、按距离排好序的“附近推荐列表”。**

## 2. 时空连续性的实现机制

时空连续性不再是离散的工具调用结果，而是**通过在`run_icp_planning`核心循环中嵌入动态计算，成为AI思考的内在属性**。

### 2.1. 实时状态管理 (Real-time State Management)
-   `current_location`: `Dict` - Agent当前所在的**空间位置**。它在每次活动被调度后，都会被精确更新为该活动所在的位置。
-   `current_time`: `str` - Agent内部维护的**虚拟时钟**，由交通耗时和活动耗时精确驱动。

### 2.2. 上下文感知的AI思考 (Context-Aware AI Thinking)

在ICP循环的每一步，Agent都会：

1.  **动态位置感知 (Pre-computation)**:
    -   **就在调用LLM之前**，系统会获取`current_location`。
    -   然后**实时计算**当前位置到`remaining_pois`池中所有剩余景点的距离。
    -   最后生成一个**按距离从近到远排序**的`nearby_poi_options`列表。

2.  **上下文注入 (Context Injection)**:
    -   这个新鲜出炉的、带有距离信息的`nearby_poi_options`列表，会作为最重要的上下文，被注入到`think_tool`的Prompt中。
    -   LLM接收到的问题因此变为：
        > "今天是第 {day} 天，现在是 {time}，我位于 {location}。**这里是我附近的可用选项(按距离排序): {nearby_poi_options}**。根据用户偏好，我下一步最应该做什么？"

### 2.3. 原子化的时空工具 (Atomic Spatio-Temporal Tools)
-   `select_poi_from_pool(poi_name)`: AI决策后，调用此工具从池中选择POI。由于AI的决策本身就是基于距离的，这个选择自然地保证了地理连续性。
-   `search_poi_by_name(poi_name)`: 当AI决策需要搜索一个池外POI时调用。**(注：此为高层封装，复用`map_tool.py`的底层搜索能力)**
-   `get_travel_time_and_distance(origin, destination)`: 在确定下一个POI后，调用此工具获取精确的交通耗时和距离。**(注：复用`map_tool.py`的`get_route`能力)**
-   `schedule_activity(poi, activity_duration_minutes)`: **这是实现时空状态更新的核心原子工具**。它的职责是：
    1.  调用`get_travel_time_and_distance`获取从`current_location`到目标`poi`的交通时间。
    2.  计算出活动的精确`start_time`和`end_time`。
    3.  将完整的活动信息（包含交通、时间、POI详情）添加至`structured_itinerary`。
    4.  **原子化地更新状态**：
        -   推进`current_time`（增加交通时间 + 活动时长）。
        -   更新`current_location`为当前活动POI的位置。
    5.  此工具的正确执行，是下一次循环中“动态位置感知”能够获取到最新时空状态的根本保证。

## 3. 工作流示例

1.  **启动**: `current_location`设为酒店, `current_time`设为`09:00`。
2.  **迭代1 (上午活动)**:
    -   **动态感知**: 系统计算出酒店附近的POI，排序后生成`nearby_poi_options` = `[(南山寺, 2km), (西山公园, 5km), ...]`。
    -   **思考**: LLM接收到包含上述`nearby_poi_options`的上下文，决策：“选择最近的南山寺”。
    -   **行动与调度**: Agent调用`select_poi_from_pool`, `get_travel_time_and_distance`。`schedule_activity`工具将活动加入日程，并更新状态：`current_location`变为“南山寺”，`current_time`变为`12:20`。
3.  **迭代2 (午餐)**:
    -   **动态感知**: 系统获取新的`current_location`(“南山寺”)，重新计算并生成新的`nearby_poi_options` = `[(附近的高分餐厅, 0.5km), (商业街美食, 1.2km), ...]`。
    -   **思考**: LLM接收到新的、与南山寺相关的“附近推荐”，决策“去附近的高分餐厅”。
    -   **行动与调度**: ... 以此类推。

## 4. 结论

在V3.1架构中，基于距离的规划不再是一个独立的优化步骤，也不是一个被动查询的工具，而是通过**在思考前进行动态预计算**，成为驱动整个ICP循环的核心能力。这种新范式让Agent的每一步决策都牢牢地建立在时空连续性的基础之上，从根本上保证了最终行程的合理性。
