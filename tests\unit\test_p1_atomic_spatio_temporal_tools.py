"""
P1阶段单元测试：原子化时空工具测试

测试四个核心原子工具的功能，使用真实数据进行验证：
1. get_travel_time_and_distance
2. calculate_nearby_pois_sorted_by_distance  
3. schedule_activity
4. search_poi_by_name

严格要求：
- 使用真实的POI数据（从高德API获取）
- 使用真实的地理位置坐标
- 调用真实的LLM进行测试
- 使用MySQL user_id=1的真实用户数据
"""

import pytest
import asyncio
import logging
from typing import Dict, Any, List

# 导入被测试的工具
from src.tools.travel_planner.icp_tools import (
    get_travel_time_and_distance,
    calculate_nearby_pois_sorted_by_distance,
    schedule_activity,
    search_poi_by_name
)
from src.tools.unified_registry import unified_registry

logger = logging.getLogger(__name__)

class TestP1AtomicSpatioTemporalTools:
    """P1阶段原子化时空工具测试类"""
    
    @pytest.fixture
    def real_beijing_locations(self):
        """真实的北京地标位置数据"""
        return {
            "tiananmen": {
                "name": "天安门广场",
                "lat": 39.903578,
                "lon": 116.397544,
                "address": "北京市东城区天安门广场"
            },
            "forbidden_city": {
                "name": "故宫博物院", 
                "lat": 39.918058,
                "lon": 116.397128,
                "address": "北京市东城区景山前街4号"
            },
            "summer_palace": {
                "name": "颐和园",
                "lat": 39.999748,
                "lon": 116.275020,
                "address": "北京市海淀区新建宫门路19号"
            },
            "temple_of_heaven": {
                "name": "天坛公园",
                "lat": 39.881925,
                "lon": 116.407394,
                "address": "北京市东城区天坛路甲1号"
            }
        }
    
    @pytest.fixture
    def real_poi_pool(self):
        """真实的POI池数据（模拟从高德API获取的格式）"""
        return [
            {
                "id": "B000A8URXB",
                "poi_id": "B000A8URXB",
                "name": "故宫博物院",
                "address": "北京市东城区景山前街4号",
                "poi_type": "ATTRACTION",
                "location": "116.397128,39.918058",
                "lat": 39.918058,
                "lon": 116.397128,
                "rating": 4.7,
                "phone_number": "010-85007421",
                "introduction": "明清两朝的皇家宫殿，世界文化遗产"
            },
            {
                "id": "B000A8VQXK",
                "poi_id": "B000A8VQXK", 
                "name": "颐和园",
                "address": "北京市海淀区新建宫门路19号",
                "poi_type": "ATTRACTION",
                "location": "116.275020,39.999748",
                "lat": 39.999748,
                "lon": 116.275020,
                "rating": 4.6,
                "phone_number": "010-62881144",
                "introduction": "中国古典园林之首，世界文化遗产"
            },
            {
                "id": "B000A8XRQM",
                "poi_id": "B000A8XRQM",
                "name": "天坛公园", 
                "address": "北京市东城区天坛路甲1号",
                "poi_type": "ATTRACTION",
                "location": "116.407394,39.881925",
                "lat": 39.881925,
                "lon": 116.407394,
                "rating": 4.5,
                "phone_number": "010-67028866",
                "introduction": "明清皇帝祭天的场所，世界文化遗产"
            }
        ]
    
    @pytest.fixture
    def sample_current_state(self, real_beijing_locations, real_poi_pool):
        """模拟当前规划状态"""
        return {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "09:00",
                "current_location": real_beijing_locations["tiananmen"],
                "is_done": False
            },
            "remaining_pois": real_poi_pool.copy(),
            "used_poi_ids": [],
            "structured_itinerary": {},
            "daily_plans": {}
        }
    
    def test_tool_registration(self):
        """测试工具是否正确注册到UnifiedToolRegistry"""
        # 检查Action Tools
        action_tools = unified_registry.get_action_tool_names()
        assert "get_travel_time_and_distance" in action_tools, "get_travel_time_and_distance未注册为Action Tool"
        assert "search_poi_by_name" in action_tools, "search_poi_by_name未注册为Action Tool"
        
        # 检查Planner Tools
        planner_tools = unified_registry.get_all_planner_tools()
        assert "calculate_nearby_pois_sorted_by_distance" in planner_tools, "calculate_nearby_pois_sorted_by_distance未注册为Planner Tool"
        assert "schedule_activity" in planner_tools, "schedule_activity未注册为Planner Tool"
        
        logger.info("✓ 所有P1工具已正确注册")
    
    def test_get_travel_time_and_distance_real_data(self, real_beijing_locations):
        """测试真实地点间的路线计算"""
        logger.info("测试get_travel_time_and_distance工具...")
        
        # 测试天安门到故宫的路线（实际距离约1.2km）
        origin = real_beijing_locations["tiananmen"]
        destination = real_beijing_locations["forbidden_city"]
        
        result = get_travel_time_and_distance(origin, destination)
        
        # 验证结果结构
        assert isinstance(result, dict), "返回结果应为字典"
        assert "success" in result, "结果应包含success字段"
        assert "duration_minutes" in result, "结果应包含duration_minutes字段"
        assert "distance_km" in result, "结果应包含distance_km字段"
        
        if result["success"]:
            # 验证距离合理性（天安门到故宫驾车路线约1-5km）
            distance = result["distance_km"]
            assert 0.5 <= distance <= 5.0, f"距离不合理：{distance}km，预期0.5-5.0km"

            # 验证时间合理性（驾车约5-30分钟）
            duration = result["duration_minutes"]
            assert 1 <= duration <= 60, f"时间不合理：{duration}分钟，预期1-60分钟"
            
            logger.info(f"✓ 路线计算成功：{distance}km，{duration}分钟")
        else:
            logger.warning(f"路线计算失败：{result.get('error', '未知错误')}")
            # 即使API失败，也要确保返回格式正确
            assert result["duration_minutes"] == 0
            assert result["distance_km"] == 0
    
    def test_calculate_nearby_pois_sorted_by_distance_real_data(self, real_beijing_locations, real_poi_pool):
        """测试真实POI的距离排序"""
        logger.info("测试calculate_nearby_pois_sorted_by_distance工具...")
        
        current_location = real_beijing_locations["tiananmen"]
        
        result = calculate_nearby_pois_sorted_by_distance(current_location, real_poi_pool)
        
        # 验证结果结构
        assert isinstance(result, list), "返回结果应为列表"
        assert len(result) == len(real_poi_pool), "返回POI数量应与输入相同"
        
        # 验证每个POI都有distance_km字段
        for poi in result:
            assert "distance_km" in poi, "每个POI应包含distance_km字段"
            assert isinstance(poi["distance_km"], (int, float)), "distance_km应为数值"
        
        # 验证排序正确性（距离递增）
        distances = [poi["distance_km"] for poi in result if poi["distance_km"] != float('inf')]
        if len(distances) > 1:
            for i in range(1, len(distances)):
                assert distances[i] >= distances[i-1], f"距离排序错误：{distances}"
        
        # 验证距离计算合理性
        # 天安门到故宫应该是最近的（直线距离约1.2km）
        closest_poi = result[0] if result else None
        if closest_poi and closest_poi["name"] == "故宫博物院":
            assert 0.5 <= closest_poi["distance_km"] <= 2.0, f"天安门到故宫直线距离不合理：{closest_poi['distance_km']}km"
        
        logger.info(f"✓ 距离排序成功，最近POI：{closest_poi['name'] if closest_poi else 'None'}({closest_poi['distance_km'] if closest_poi else 0}km)")

    def test_schedule_activity_real_data(self, real_beijing_locations, real_poi_pool, sample_current_state):
        """测试活动调度的原子化时空状态更新"""
        logger.info("测试schedule_activity工具...")

        # 选择故宫作为要调度的活动
        forbidden_city_poi = real_poi_pool[0]  # 故宫博物院
        activity_duration = 180  # 3小时

        result = schedule_activity(forbidden_city_poi, activity_duration, sample_current_state)

        # 验证结果结构
        assert isinstance(result, dict), "返回结果应为字典"
        assert "success" in result, "结果应包含success字段"

        if result["success"]:
            # 验证活动对象
            activity = result["activity"]
            assert activity["name"] == "故宫博物院", "活动名称应正确"
            assert activity["duration_minutes"] == 180, "活动时长应正确"
            assert "start_time" in activity, "应包含开始时间"
            assert "end_time" in activity, "应包含结束时间"
            assert "transport_to" in activity, "应包含交通信息"

            # 验证时空状态更新
            updated_icp_state = result["updated_icp_state"]
            assert "current_time" in updated_icp_state, "应更新当前时间"
            assert "current_location" in updated_icp_state, "应更新当前位置"

            # 验证位置更新正确
            new_location = updated_icp_state["current_location"]
            assert new_location["name"] == "故宫博物院", "当前位置应更新为故宫"

            # 验证行程添加
            itinerary = result["updated_structured_itinerary"]
            assert 1 in itinerary, "应包含第1天的行程"
            assert len(itinerary[1]) == 1, "第1天应有1个活动"

            logger.info(f"✓ 活动调度成功：{activity['start_time']}-{activity['end_time']}")
        else:
            logger.warning(f"活动调度失败：{result.get('error', '未知错误')}")

    def test_search_poi_by_name_real_data(self):
        """测试真实POI搜索"""
        logger.info("测试search_poi_by_name工具...")

        # 搜索北京的真实景点
        poi_name = "天坛"
        city = "北京"

        result = search_poi_by_name(poi_name, city)

        # 验证结果结构
        assert isinstance(result, list), "返回结果应为列表"

        if result:
            # 验证第一个结果的结构
            first_poi = result[0]
            required_fields = ["id", "name", "address", "poi_type", "location", "lat", "lon"]
            for field in required_fields:
                assert field in first_poi, f"POI应包含{field}字段"

            # 验证搜索结果相关性
            assert "天坛" in first_poi["name"], f"搜索结果应包含'天坛'，实际：{first_poi['name']}"

            # 验证位置格式
            location = first_poi["location"]
            assert "," in location, "位置格式应为'lng,lat'"

            # 验证坐标合理性（北京范围）
            lat, lon = first_poi["lat"], first_poi["lon"]
            assert 39.0 <= lat <= 41.0, f"纬度应在北京范围内：{lat}"
            assert 115.0 <= lon <= 118.0, f"经度应在北京范围内：{lon}"

            logger.info(f"✓ POI搜索成功，找到{len(result)}个结果，第一个：{first_poi['name']}")
        else:
            logger.warning("POI搜索返回空结果")

    def test_integration_workflow_real_data(self, real_beijing_locations, sample_current_state):
        """测试P1工具的集成工作流"""
        logger.info("测试P1工具集成工作流...")

        # 1. 搜索新POI
        search_results = search_poi_by_name("北海公园", "北京")
        if not search_results:
            logger.warning("搜索结果为空，跳过集成测试")
            return

        new_poi = search_results[0]

        # 2. 将新POI添加到剩余池中
        updated_poi_pool = sample_current_state["remaining_pois"] + [new_poi]

        # 3. 计算距离排序
        current_location = sample_current_state["icp_planner_state"]["current_location"]
        sorted_pois = calculate_nearby_pois_sorted_by_distance(current_location, updated_poi_pool)

        # 4. 选择最近的POI进行调度
        if sorted_pois:
            nearest_poi = sorted_pois[0]
            schedule_result = schedule_activity(nearest_poi, 120, sample_current_state)

            if schedule_result["success"]:
                # 5. 验证整个工作流的连贯性
                activity = schedule_result["activity"]
                updated_state = schedule_result["updated_icp_state"]

                # 验证时空连续性
                assert activity["name"] == nearest_poi["name"], "调度的活动应为最近的POI"
                assert updated_state["current_location"]["name"] == nearest_poi["name"], "位置应更新为活动地点"

                logger.info(f"✓ 集成工作流成功：搜索→排序→调度 {activity['name']}")
            else:
                logger.warning("活动调度失败")
        else:
            logger.warning("距离排序返回空结果")
