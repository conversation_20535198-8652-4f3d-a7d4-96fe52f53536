["tests/e2e/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_end_to_end_p1_p2_p3_integration", "tests/e2e/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_generate_planning_thought_with_nearby_options", "tests/e2e/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_llm_geographic_decision_making", "tests/e2e/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_prompt_distance_guidance_injection", "tests/integration/test_icp_planning_flow.py::test_run_icp_planning_with_mocked_llm", "tests/integration/test_p2_icp_planning_flow.py::TestP2ICPPlanningFlow::test_run_icp_planning_complete_flow", "tests/integration/test_p2_icp_planning_flow.py::TestP2ICPPlanningFlow::test_run_icp_planning_dynamic_location_awareness", "tests/integration/test_p2_icp_planning_flow.py::TestP2ICPPlanningFlow::test_run_icp_planning_error_handling", "tests/test_api_v3_integration.py::TestAPIV3Integration::test_get_tools_endpoint", "tests/test_api_v3_integration.py::TestAPIV3Integration::test_health_check_endpoint", "tests/test_icp_planning.py::TestICPPlanning::test_action_selection", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_missing_context", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_missing_tools", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_success", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_tool_failure_handling", "tests/test_icp_planning.py::TestICPPlanning::test_icp_planning_with_multiple_iterations", "tests/test_icp_planning.py::TestICPPlanning::test_icp_tools_registration", "tests/test_icp_planning.py::TestICPPlanning::test_planning_thought_generation", "tests/test_icp_workflow.py::TestPlannerAgentNode::test_planner_agent_basic_decision", "tests/test_icp_workflow.py::TestPlannerAgentNode::test_planner_agent_error_handling", "tests/test_icp_workflow.py::TestPlannerAgentNode::test_planner_agent_json_parsing_fallback", "tests/test_icp_workflow.py::TestShouldContinuePlanning::test_should_continue_with_incomplete_planning", "tests/test_icp_workflow.py::TestShouldContinuePlanning::test_should_end_with_complete_planning", "tests/test_icp_workflow.py::TestShouldContinuePlanning::test_should_end_with_error", "tests/test_icp_workflow.py::TestShouldContinuePlanning::test_should_end_with_finish_planning_command", "tests/test_icp_workflow.py::TestShouldContinuePlanning::test_should_end_with_max_iterations", "tests/test_icp_workflow.py::TestToolExecutorNode::test_tool_executor_error_handling", "tests/test_icp_workflow.py::TestToolExecutorNode::test_tool_executor_missing_action", "tests/test_icp_workflow.py::TestToolExecutorNode::test_tool_executor_search_poi", "tests/test_icp_workflow.py::TestTravelPlannerGraphV3::test_graph_creation", "tests/test_icp_workflow.py::TestTravelPlannerGraphV3::test_run_unified_basic", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_complete_intent_analysis_flow", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_context_preparation_missing_analysis", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_framework_analysis_missing_tools", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_framework_analysis_success", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_preference_analysis_missing_framework", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_preference_analysis_success", "tests/test_intent_analysis_flow.py::TestIntentAnalysisFlow::test_prepare_planning_context_success", "tests/test_performance.py::TestPerformance::test_concurrent_requests", "tests/test_performance.py::TestPerformance::test_event_bus_performance", "tests/test_performance.py::TestPerformance::test_multiple_requests_stability", "tests/test_performance.py::TestPerformance::test_response_time_single_request", "tests/test_performance.py::TestPerformance::test_tool_registry_performance", "tests/test_unified_architecture.py::TestStandardAgentState::test_state_structure", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_initialize_task", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_error", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_phase_end", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_notify_phase_start", "tests/test_unified_architecture.py::TestUnifiedEventBus::test_sync_from_agent_state", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_execute_action_tool", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_execute_action_tool_with_event_bus", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_get_tool_info", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_register_action_tool", "tests/test_unified_architecture.py::TestUnifiedToolRegistry::test_register_planner_tool", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_complete_workflow_success", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_error_handling_in_workflow", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_event_bus_integration", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_state_synchronization", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_streaming_workflow", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_tool_registry_integration", "tests/test_unified_architecture_e2e.py::TestUnifiedArchitectureE2E::test_workflow_with_real_data_simulation", "tests/unit/test_icp_tools.py::test_calculate_nearby_pois_sorted_by_distance_real", "tests/unit/test_icp_tools.py::test_get_travel_time_and_distance_real", "tests/unit/test_icp_tools.py::test_schedule_activity_real", "tests/unit/test_icp_tools.py::test_search_poi_by_name_real", "tests/unit/test_p1_atomic_spatio_temporal_tools.py::TestP1AtomicSpatioTemporalTools::test_calculate_nearby_pois_sorted_by_distance_real_data", "tests/unit/test_p1_atomic_spatio_temporal_tools.py::TestP1AtomicSpatioTemporalTools::test_get_travel_time_and_distance_real_data", "tests/unit/test_p1_atomic_spatio_temporal_tools.py::TestP1AtomicSpatioTemporalTools::test_integration_workflow_real_data", "tests/unit/test_p1_atomic_spatio_temporal_tools.py::TestP1AtomicSpatioTemporalTools::test_schedule_activity_real_data", "tests/unit/test_p1_atomic_spatio_temporal_tools.py::TestP1AtomicSpatioTemporalTools::test_search_poi_by_name_real_data", "tests/unit/test_p1_atomic_spatio_temporal_tools.py::TestP1AtomicSpatioTemporalTools::test_tool_registration", "tests/unit/test_p1_atomic_tools.py::TestP1AtomicTools::test_atomic_tools_registration", "tests/unit/test_p1_atomic_tools.py::TestP1AtomicTools::test_calculate_nearby_pois_sorted_by_distance", "tests/unit/test_p1_atomic_tools.py::TestP1AtomicTools::test_get_travel_time_and_distance_fallback", "tests/unit/test_p1_atomic_tools.py::TestP1AtomicTools::test_get_travel_time_and_distance_success", "tests/unit/test_p1_atomic_tools.py::TestP1AtomicTools::test_schedule_activity_success", "tests/unit/test_p1_atomic_tools.py::TestP1AtomicTools::test_search_poi_by_name_not_found", "tests/unit/test_p1_atomic_tools.py::TestP1AtomicTools::test_search_poi_by_name_success", "tests/unit/test_p2_core_planning_loop.py::TestP2CorePlanningLoop::test_dynamic_location_awareness_logic", "tests/unit/test_p2_core_planning_loop.py::TestP2CorePlanningLoop::test_p1_tools_integration", "tests/unit/test_p2_core_planning_loop.py::TestP2CorePlanningLoop::test_schedule_activity_integration", "tests/unit/test_p2_core_planning_loop.py::TestP2CorePlanningLoop::test_think_decide_act_schedule_workflow", "tests/unit/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_context_for_prompt_enhancement", "tests/unit/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_distance_priority_calculation", "tests/unit/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_location_based_decision_logic", "tests/unit/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_nearby_poi_options_context_integration", "tests/unit/test_p3_ai_thinking_optimization.py::TestP3AIThinkingOptimization::test_prompt_template_optimization", "tests/unit/test_v3_refactored_components.py::TestICPIterativePlanning::test_planner_agent_node", "tests/unit/test_v3_refactored_components.py::TestICPIterativePlanning::test_route_icp_action", "tests/unit/test_v3_refactored_components.py::TestICPIterativePlanning::test_tool_executor_node", "tests/unit/test_v3_refactored_components.py::TestTwoStageWorkflowNodes::test_framework_analysis_node", "tests/unit/test_v3_refactored_components.py::TestTwoStageWorkflowNodes::test_preference_analysis_node", "tests/unit/test_v3_refactored_components.py::TestTwoStageWorkflowNodes::test_prepare_planning_context_node", "tests/unit/test_v3_refactored_components.py::TestUnifiedToolRegistry::test_action_tools_registration", "tests/unit/test_v3_refactored_components.py::TestUnifiedToolRegistry::test_planner_tools_registration", "tests/unit/test_v3_refactored_components.py::TestUnifiedToolRegistry::test_tool_info_retrieval", "tests/unit/test_v3_refactored_components.py::TestUnifiedToolRegistry::test_tool_registry_initialization", "tests/unit/test_v3_refactored_components.py::TestV3APIIntegration::test_api_router_configuration", "tests/unit/test_v3_refactored_components.py::TestV3APIIntegration::test_plan_request_model"]