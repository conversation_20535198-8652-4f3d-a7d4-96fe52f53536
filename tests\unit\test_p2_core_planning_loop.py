"""
P2阶段单元测试：重构核心规划循环测试

测试run_icp_planning节点函数的核心功能：
1. 动态位置感知逻辑
2. Think→Decide→Act→Schedule流程
3. P1原子工具的集成
4. 时空状态的原子化更新

严格要求：
- 使用真实数据进行测试
- 验证与P1阶段原子工具的正确集成
- 确保时空连续性保证
"""

import pytest
import asyncio
import logging
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch

# 导入被测试的函数
from src.agents.travel_planner_lg.nodes import run_icp_planning
from src.agents.travel_planner_lg.state import StandardAgentState
from src.tools.unified_registry import unified_registry

logger = logging.getLogger(__name__)

class TestP2CorePlanningLoop:
    """P2阶段核心规划循环测试类"""
    
    @pytest.fixture
    def mock_state(self):
        """模拟StandardAgentState"""
        return {
            "task_id": "test_task_001",
            "notification_service": None,  # 简化测试，不使用事件总线
            "icp_context": {
                "planning_goals": ["景点游览", "美食体验"],
                "constraints": {"max_days": 2, "budget_limit": 1000}
            },
            "consolidated_intent": {
                "travel_days": 2,
                "destinations": ["北京"],
                "preferences": {
                    "attractions": ["历史文化"],
                    "food": ["本地特色"]
                }
            },
            "daily_plans": {},
            "daily_time_tracker": {},
            "total_budget_tracker": 0.0,
            "tool_results": {},
            "planning_log": []
        }
    
    @pytest.fixture
    def mock_poi_pool(self):
        """模拟POI池数据"""
        return [
            {
                "id": "B000A8URXB",
                "poi_id": "B000A8URXB",
                "name": "故宫博物院",
                "address": "北京市东城区景山前街4号",
                "poi_type": "ATTRACTION",
                "location": "116.397128,39.918058",
                "lat": 39.918058,
                "lon": 116.397128,
                "rating": 4.7,
                "phone_number": "010-85007421",
                "introduction": "明清两朝的皇家宫殿，世界文化遗产"
            },
            {
                "id": "B000A8VQXK",
                "poi_id": "B000A8VQXK", 
                "name": "颐和园",
                "address": "北京市海淀区新建宫门路19号",
                "poi_type": "ATTRACTION",
                "location": "116.275020,39.999748",
                "lat": 39.999748,
                "lon": 116.275020,
                "rating": 4.6,
                "phone_number": "010-62881144",
                "introduction": "中国古典园林之首，世界文化遗产"
            }
        ]
    
    @pytest.fixture
    def mock_base_hotel(self):
        """模拟基点酒店"""
        return {
            "name": "北京王府井酒店",
            "lat": 39.903578,
            "lon": 116.397544,
            "address": "北京市东城区王府井大街",
            "poi_id": "hotel_001"
        }
    
    def test_p1_tools_integration(self):
        """测试P1阶段工具是否正确集成到规划循环中"""
        logger.info("测试P1工具集成...")
        
        # 验证P1工具在UnifiedToolRegistry中注册
        required_p1_tools = [
            "calculate_nearby_pois_sorted_by_distance",
            "schedule_activity"
        ]
        
        planner_tools = unified_registry.get_all_planner_tools()
        for tool_name in required_p1_tools:
            assert tool_name in planner_tools, f"P1工具{tool_name}未在规划循环中集成"
        
        logger.info("✓ P1工具集成验证通过")
    
    @pytest.mark.asyncio
    async def test_dynamic_location_awareness_logic(self, mock_state, mock_poi_pool, mock_base_hotel):
        """测试动态位置感知逻辑"""
        logger.info("测试动态位置感知逻辑...")
        
        # 模拟规划状态
        planning_state = {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "09:00",
                "current_location": mock_base_hotel,
                "is_done": False
            },
            "remaining_pois": mock_poi_pool.copy(),
            "used_poi_ids": [],
            "consolidated_intent": mock_state["consolidated_intent"]
        }
        
        # 获取距离计算工具
        distance_tool = unified_registry.get_planner_tool("calculate_nearby_pois_sorted_by_distance")
        assert distance_tool is not None, "距离计算工具未找到"
        
        # 执行动态位置感知
        current_location = planning_state["icp_planner_state"]["current_location"]
        remaining_pois = planning_state["remaining_pois"]
        
        nearby_poi_options = distance_tool(current_location, remaining_pois)
        
        # 验证结果
        assert isinstance(nearby_poi_options, list), "距离计算结果应为列表"
        assert len(nearby_poi_options) == len(remaining_pois), "返回POI数量应与输入相同"
        
        # 验证距离字段
        for poi in nearby_poi_options:
            assert "distance_km" in poi, "每个POI应包含distance_km字段"
            assert isinstance(poi["distance_km"], (int, float)), "distance_km应为数值"
        
        # 验证排序
        distances = [poi["distance_km"] for poi in nearby_poi_options if poi["distance_km"] != float('inf')]
        if len(distances) > 1:
            for i in range(1, len(distances)):
                assert distances[i] >= distances[i-1], "POI应按距离排序"
        
        logger.info(f"✓ 动态位置感知成功，最近POI：{nearby_poi_options[0]['name']}({nearby_poi_options[0]['distance_km']:.2f}km)")
    
    @pytest.mark.asyncio
    async def test_schedule_activity_integration(self, mock_poi_pool, mock_base_hotel):
        """测试schedule_activity工具的集成"""
        logger.info("测试schedule_activity工具集成...")
        
        # 模拟当前状态
        current_state = {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "09:00",
                "current_location": mock_base_hotel,
                "is_done": False
            },
            "structured_itinerary": {},
            "remaining_pois": mock_poi_pool.copy(),
            "used_poi_ids": []
        }
        
        # 获取调度工具
        schedule_tool = unified_registry.get_planner_tool("schedule_activity")
        assert schedule_tool is not None, "schedule_activity工具未找到"
        
        # 选择一个POI进行调度
        selected_poi = mock_poi_pool[0]  # 故宫博物院
        activity_duration = 180  # 3小时
        
        # 执行活动调度
        schedule_result = schedule_tool(selected_poi, activity_duration, current_state)
        
        # 验证调度结果
        assert isinstance(schedule_result, dict), "调度结果应为字典"
        assert "success" in schedule_result, "调度结果应包含success字段"
        
        if schedule_result["success"]:
            # 验证活动对象
            activity = schedule_result["activity"]
            assert activity["name"] == "故宫博物院", "活动名称应正确"
            assert activity["duration_minutes"] == 180, "活动时长应正确"
            assert "start_time" in activity, "应包含开始时间"
            assert "end_time" in activity, "应包含结束时间"
            
            # 验证时空状态更新
            updated_state = schedule_result["updated_icp_state"]
            assert "current_time" in updated_state, "应更新当前时间"
            assert "current_location" in updated_state, "应更新当前位置"
            
            # 验证原子化更新
            new_location = updated_state["current_location"]
            assert new_location["name"] == "故宫博物院", "当前位置应原子化更新"
            
            logger.info(f"✓ 活动调度成功：{activity['start_time']}-{activity['end_time']}")
        else:
            logger.warning(f"活动调度失败：{schedule_result.get('error', '未知错误')}")
    
    @pytest.mark.asyncio 
    async def test_think_decide_act_schedule_workflow(self, mock_state, mock_poi_pool, mock_base_hotel):
        """测试Think→Decide→Act→Schedule完整工作流"""
        logger.info("测试Think→Decide→Act→Schedule工作流...")
        
        # 由于完整的run_icp_planning函数涉及复杂的LLM调用和多个工具集成
        # 这里主要测试工作流的关键组件是否正确注册和可调用
        
        # 验证Think工具
        think_tool = unified_registry.get_planner_tool("generate_planning_thought")
        assert think_tool is not None, "generate_planning_thought工具未找到"
        
        # 验证Action工具
        action_tools = unified_registry.get_action_tool_names()
        required_action_tools = ["search_poi_by_name", "get_travel_time_and_distance"]
        for tool_name in required_action_tools:
            assert tool_name in action_tools, f"Action工具{tool_name}未注册"
        
        # 验证Planner工具
        planner_tools = unified_registry.get_all_planner_tools()
        required_planner_tools = [
            "select_next_action",
            "observe_action_result", 
            "update_planning_state",
            "check_planning_completion"
        ]
        for tool_name in required_planner_tools:
            assert tool_name in planner_tools, f"Planner工具{tool_name}未注册"
        
        logger.info("✓ Think→Decide→Act→Schedule工作流组件验证通过")
