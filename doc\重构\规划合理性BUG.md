# 修复规划合理性BUG与架构升级计划 (V2.0)

## 1. 问题诊断 (Problem Diagnosis)

### 1.1. 核心问题
当前行程规划逻辑的本质是**“静态POI篮子里的粗放式分配”**，它缺乏对时空连续性的深刻理解，导致规划结果机械且不合理。

### 1.2. 具体症状
- **无时间概念**: 无法为活动分配合理的时间点（如`09:00`, `12:30`）。
- **无空间概念**: 无法动态感知`current_location`的变化，导致路线规划在城市两端反复横跳。
- **无动态应变能力**: 当初始POI池不包含用户想去的特定地点（如“湄洲岛”）时，系统会卡死，而不是主动去搜索。
- **搜索范围僵化**: 初始POI搜索范围过于简单（如按城市搜索），导致POI池质量不高，从源头上就缺少了关键地点。

## 2. 解决方案：两阶段智能规划架构

我们将对规划流程进行根本性重构，引入一个两阶段架构：**“智能POI侦察”** + **“每日时序规划”**。

### 阶段一：智能POI侦察 (Intelligent POI Scouting)

此阶段的目标是构建一个**高质量、高覆盖率**的初始`poi_pool`，为后续规划打下坚实基础。此阶段在所有迭代规划开始前执行，并**完全复用**`tools/Amap/map_tool.py`中定义的底层搜索能力。

1.  **识别核心锚点 (Identify Core Anchors)**:
    -   使用强大LLM（`reasoning`模型）分析用户需求，识别出行程中所有关键的**地理锚点**（如“故宫”、“环球影城”、“川西”）和核心兴趣关键词（如“博物馆”、“烤鸭”）。

2.  **执行智能扩展搜索 (Intelligent Expansion Search)**:
    -   **多中心搜索**: 以每个地理锚点为圆心，调用地图服务（该服务封装了`map_tool.py`中的`search_poi_around`等方法）进行周边搜索。
    -   **动态半径**: 根据锚点区域的POI密度，动态调整搜索半径（市中心用小半径，郊区用大半径）。
    -   **关键词补充**: 使用核心兴趣关键词，进行全城范围的补充搜索（同样复用`map_tool.py`的`search_pois`能力）。

3.  **聚合与去重 (Aggregate & De-duplicate)**:
    -   将所有搜索结果汇集，进行严格去重，形成最终的、内容丰富的`poi_pool`，并存入`TravelPlannerState`。

### 阶段二：每日时序规划 (Daily Sequential Planning)

此阶段在`run_icp_planning`节点中实现，是整个规划的核心。它将Agent的思考模式从“规划一整天”转变为“**基于当前时空，规划下一个活动**”。

1.  **双重循环结构**:
    -   **外循环**: 遍历旅行的每一天 (`for day in total_days`)。
    -   **内循环**: 基于时间进行迭代，直到一天的结束 (`while current_time < end_of_day_time`)。

2.  **核心循环状态 (在`icp_planner_state`中管理)**:
    -   `current_day`: `int`
    -   `current_time`: `str` (e.g., "09:00")
    -   `current_location`: `Dict` (包含`name`, `lat`, `lon`)

3.  **单步迭代流程 (Think -> Decide -> Act -> Schedule)**:
    1.  **动态位置感知 (Pre-computation)**:
        -   在调用LLM**之前**，实时计算`current_location`到`poi_pool`中所有剩余景点的距离，并生成一个**按距离排序的`nearby_poi_options`列表**。
    2.  **思考 (Think)**:
        -   将`current_time`, `current_location`, `nearby_poi_options`等信息传递给`generate_planning_thought`工具。
        -   LLM被引导回答：“基于我当前的时空和附近的选项，下一步最该做什么？”
    3.  **决策 (Decide)**:
        -   LLM输出一个包含明确**工具名称**和**参数**的行动指令。
        -   **智能工具选择**:
            -   如果想去的POI在`nearby_poi_options`中，LLM选择`select_poi_from_pool`。
            -   如果想去的特定POI不在池中，LLM选择`search_poi_by_name`。
            -   如果时间已晚，LLM选择`end_day_planning`。
    4.  **行动 (Act)**:
        -   Agent精确执行LLM返回的工具指令。
    5.  **调度 (Schedule)**:
        -   调用`get_driving_time`等工具获取交通耗时。
        -   将包含明确**起止时间**的活动添加至当天的计划中。
        -   精确更新`current_time`和`current_location`，为下一次迭代做准备。

## 3. 文件修改清单 (V2.0)

### 3.1. 核心逻辑层: `src/agents/travel_planner_lg/nodes.py`
-   **新增 `run_intelligent_poi_scouting` 节点/函数**:
    -   实现“智能POI侦察”阶段的完整逻辑，负责在规划初期高质量地填充`state.poi_pool`。
-   **重构 `run_icp_planning` 函数**:
    -   **实现动态位置感知**: 在`while`循环的开始，必须添加代码来实时计算`current_location`到`remaining_pois`中每个POI的距离，并生成一个按距离排序的`nearby_poi_options`列表。
    -   **打通核心循环**: 严格按照`think_tool` -> `action_tool` -> `schedule_activity`的流程串联。确保`current_time`和`current_location`在每次迭代后，都由`schedule_activity`工具进行精确、原子化的更新。

### 3.2. 工具层: `src/tools/travel_planner/icp_tools.py`
-   **改造 `generate_planning_thought` 工具**:
    -   **输入**: 必须接收按距离实时排序的`nearby_poi_options`作为关键输入。
    -   **Prompt**: 需要被重构，明确告知LLM其拥有的`available_actions`，并引导它基于时空上下文（特别是`nearby_poi_options`）做出决策。
-   **新增 `search_poi_by_name` 工具**:
    -   **职责**: 作为Agent的高层行动工具，用于处理规划中途发现的、用户想去但不在初始POI池中的兴趣点。
    -   **实现**: **【重要】此工具为高层封装，绝不重写搜索逻辑。** 其内部将调用`AmapService`，而该服务最终会调用`tools/Amap/map_tool.py`中定义的`search_pois`方法来完成实际的地图API请求。
-   **新增 `get_travel_time_and_distance` 工具**:
    -   **职责**: 获取两个地理位置之间的预估交通耗时（分钟）和距离（公里）。
    -   **实现**: 复用`map_tool.py`中已有的`get_route`能力。
-   **新增 `schedule_activity` (原子化调度) 工具**:
    -   **职责**: 这是驱动循环状态更新的核心，必须实现为一个原子操作。
    -   **流程**:
        1.  接收一个被选定的POI和活动预估时长。
        2.  调用`get_travel_time_and_distance`获取从`current_location`到该POI的交通时间。
        3.  将包含交通信息、**明确起止时间**的活动条目，添加至`structured_itinerary`中当天的计划列表。
        4.  精确更新`icp_planner_state.current_time` (当前时间 + 交通耗时 + 活动时长)。
        5.  精确更新`icp_planner_state.current_location`为当前已调度活动的POI位置。
-   **修改 `select_poi_from_pool` 工具**:
    -   确保它在选择POI后，能正确地从`remaining_pois`中移除该POI，并将其ID添加到`used_poi_ids`，防止重复规划。

### 3.3. 状态模型定义 (State Schema Definition)

为支持新的两阶段规划架构，`TravelPlannerState`（无论其在哪个文件中定义）必须包含以下核心字段和数据结构。这部分内容作为编码的参考标准。

-   **`poi_pool: List[Dict]`**:
    -   **职责**: 存储“智能POI侦察”阶段获取的所有候选POI的权威列表。
    -   **生命周期**: 在规划开始时被一次性填充，之后为只读状态。

-   **`remaining_pois: List[Dict]`**:
    -   **职责**: 存储`poi_pool`中尚未被规划的POI。
    -   **生命周期**: 在规划开始时被初始化为`poi_pool`的拷贝，之后在每次`select_poi_from_pool`或`search_poi_by_name`成功后，都会从中移除或添加元素。它是动态消耗的。

-   **`used_poi_ids: List[str]`**:
    -   **职责**: 存储已被成功调度（Scheduled）的POI的唯一ID。
    -   **生命周期**: 用于在整个规划流程中实现高效、可靠的去重，防止同一地点被多次规划。

-   **`icp_planner_state: Optional[Dict[str, Any]]`**:
    -   **职责**: 存储“每日时序规划”内循环的实时状态。
    -   **结构**: 必须包含以下字段：
        ```json
        {
          "current_day": 1,
          "current_time": "09:00",
          "current_location": {"name": "酒店名称", "lat": 39.9, "lon": 116.4},
          "is_done": false,
          "failure_count": 0
        }
        ```

-   **`structured_itinerary: Dict[int, List[Dict]]`**:
    -   **职责**: 存储最终生成的、包含精确时序的结构化行程。
    -   **结构**: 键为天数（`int`），值为活动列表。每个活动都是一个字典，必须包含交通、起止时间和POI信息。
        ```json
        {
          "1": [
            {
              "activity_type": "attraction",
              "start_time": "09:20",
              "end_time": "12:20",
              "poi_details": { "...POI信息..." },
              "transport_to": { "duration_minutes": 20, "distance_km": 5.3 }
            }
          ],
          "2": []
        }
        ```