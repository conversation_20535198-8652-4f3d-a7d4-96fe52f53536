"""
ICP (迭代式上下文规划) 工具集 (V3.0 - 统一架构版)

实现"思考-行动-观察"循环所需的Planner Tools，
支持AI驱动的迭代式旅行规划。

P1阶段新增：原子化时空工具，实现时空连续性保证
"""

import json
import logging
import math
import random
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from src.tools.unified_registry import unified_registry
from src.models.poi import EnrichedPOI, BasicPOI, POIType, convert_basic_to_enriched
from src.core.llm_manager import LLMManager
from src.prompts.loader import get_travel_planner_prompt
from tools.Amap.map_tool import MapTool, Location

logger = logging.getLogger(__name__)

# 初始化地图工具实例（复用底层能力）
_map_tool_instance = None

def _get_map_tool() -> MapTool:
    """获取地图工具实例（单例模式）"""
    global _map_tool_instance
    if _map_tool_instance is None:
        _map_tool_instance = MapTool()
    return _map_tool_instance


# ==================== P1阶段：原子化时空工具 ====================

@unified_registry.register_action_tool
def get_travel_time_and_distance(
    origin: Dict[str, Any],
    destination: Dict[str, Any],
    transport_mode: str = "driving"
) -> Dict[str, Any]:
    """
    P1.1 获取两点间的驾驶时间和距离

    复用tools/Amap/map_tool.py的get_route能力，提供原子化的时空计算服务。

    Args:
        origin: 起点位置对象，包含name、lat、lon字段
        destination: 终点位置对象，包含name、lat、lon字段
        transport_mode: 交通方式，默认为"driving"

    Returns:
        {"duration_minutes": 20, "distance_km": 5.3, "success": True}
    """
    try:
        logger.info(f"计算路线：{origin.get('name', '起点')} -> {destination.get('name', '终点')}")

        # 构建Location对象
        origin_location = Location(
            latitude=float(origin.get('lat', origin.get('latitude', 0))),
            longitude=float(origin.get('lon', origin.get('lng', origin.get('longitude', 0)))),
            name=origin.get('name', '起点')
        )

        destination_location = Location(
            latitude=float(destination.get('lat', destination.get('latitude', 0))),
            longitude=float(destination.get('lon', destination.get('lng', destination.get('longitude', 0)))),
            name=destination.get('name', '终点')
        )

        # 调用地图工具获取路线
        map_tool = _get_map_tool()
        route_result = map_tool.get_route(
            origin=origin_location,
            destination=destination_location,
            transport_mode=transport_mode
        )

        # 解析路线结果
        if route_result and "paths" in route_result and route_result["paths"]:
            path = route_result["paths"][0]  # 取第一条路径
            duration_seconds = int(path.get("duration", 0))
            distance_meters = int(path.get("distance", 0))

            # 转换为分钟和公里
            duration_minutes = round(duration_seconds / 60, 1)
            distance_km = round(distance_meters / 1000, 2)

            result = {
                "duration_minutes": duration_minutes,
                "distance_km": distance_km,
                "success": True,
                "raw_duration_seconds": duration_seconds,
                "raw_distance_meters": distance_meters
            }

            logger.info(f"路线计算成功：距离 {distance_km}km，时间 {duration_minutes}分钟")
            return result
        else:
            logger.warning("路线计算返回空结果")
            return {
                "duration_minutes": 0,
                "distance_km": 0,
                "success": False,
                "error": "路线计算返回空结果"
            }

    except Exception as e:
        logger.error(f"路线计算失败：{str(e)}")
        return {
            "duration_minutes": 0,
            "distance_km": 0,
            "success": False,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def calculate_nearby_pois_sorted_by_distance(
    current_location: Dict[str, Any],
    remaining_pois: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    P1.2 实现动态位置感知的核心计算逻辑

    计算当前位置到所有剩余POI的距离，并按距离排序返回。

    Args:
        current_location: 当前位置，包含lat、lon字段
        remaining_pois: 剩余POI池

    Returns:
        按距离排序的POI列表，每个POI增加distance_km字段
    """
    try:
        logger.info(f"计算附近POI距离，当前位置：{current_location.get('name', '未知')}")

        if not remaining_pois:
            logger.warning("剩余POI池为空")
            return []

        # 获取当前位置坐标
        current_lat = float(current_location.get('lat', current_location.get('latitude', 0)))
        current_lon = float(current_location.get('lon', current_location.get('lng', current_location.get('longitude', 0))))

        if current_lat == 0 or current_lon == 0:
            logger.warning("当前位置坐标无效，返回原始POI列表")
            return remaining_pois.copy()

        current_loc_str = f"{current_lon},{current_lat}"

        # 为每个POI计算距离
        pois_with_distance = []
        for poi in remaining_pois:
            poi_copy = poi.copy()

            # 获取POI位置
            poi_location = poi.get('location', '')
            if poi_location and ',' in poi_location:
                # 计算距离
                distance_km = _calculate_haversine_distance(current_loc_str, poi_location)
                poi_copy['distance_km'] = round(distance_km, 2)
            else:
                # 无效位置，设置为无穷大距离
                poi_copy['distance_km'] = float('inf')
                logger.warning(f"POI {poi.get('name', 'Unknown')} 位置信息无效：{poi_location}")

            pois_with_distance.append(poi_copy)

        # 按距离排序（距离近的在前）
        pois_with_distance.sort(key=lambda x: x.get('distance_km', float('inf')))

        # 记录排序结果
        valid_pois = [p for p in pois_with_distance if p.get('distance_km', float('inf')) != float('inf')]
        logger.info(f"距离计算完成，有效POI: {len(valid_pois)}个，最近距离: {valid_pois[0].get('distance_km', 0):.2f}km" if valid_pois else "无有效POI")

        return pois_with_distance

    except Exception as e:
        logger.error(f"POI距离计算失败：{str(e)}")
        # 返回原始列表，避免流程中断
        return remaining_pois.copy()


@unified_registry.register_planner_tool
def schedule_activity(
    poi: Dict[str, Any],
    activity_duration_minutes: int,
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    P1.3 原子化时空状态更新的核心工具

    将一个已选定的活动精确地安排到日程中，并原子化更新时空状态。

    Args:
        poi: 已选定的POI对象
        activity_duration_minutes: 活动时长（分钟）
        current_state: 当前规划状态

    Returns:
        更新后的状态信息
    """
    try:
        logger.info(f"调度活动：{poi.get('name', '未知活动')}，时长：{activity_duration_minutes}分钟")

        # 获取当前时空状态
        icp_state = current_state.get("icp_planner_state", {})
        current_location = icp_state.get("current_location", {})
        current_time = icp_state.get("current_time", "09:00")
        current_day = icp_state.get("current_day", 1)

        # 1. 计算交通时间和距离
        travel_info = get_travel_time_and_distance(current_location, poi)
        travel_duration_minutes = travel_info.get("duration_minutes", 0)
        travel_distance_km = travel_info.get("distance_km", 0)

        # 2. 计算活动的精确时间
        # 解析当前时间
        try:
            current_hour, current_minute = map(int, current_time.split(':'))
            current_total_minutes = current_hour * 60 + current_minute
        except:
            logger.warning(f"时间格式解析失败：{current_time}，使用默认09:00")
            current_total_minutes = 9 * 60  # 09:00

        # 计算活动开始时间（当前时间 + 交通时间）
        start_total_minutes = current_total_minutes + travel_duration_minutes
        start_hour = start_total_minutes // 60
        start_minute = start_total_minutes % 60
        start_time = f"{start_hour:02d}:{start_minute:02d}"

        # 计算活动结束时间
        end_total_minutes = start_total_minutes + activity_duration_minutes
        end_hour = end_total_minutes // 60
        end_minute = end_total_minutes % 60
        end_time = f"{end_hour:02d}:{end_minute:02d}"

        # 3. 构建完整的活动对象
        activity = {
            "poi_id": poi.get("poi_id", poi.get("id", "")),
            "name": poi.get("name", "未知活动"),
            "address": poi.get("address", ""),
            "poi_type": poi.get("poi_type", "ATTRACTION"),
            "location": poi.get("location", ""),
            "start_time": start_time,
            "end_time": end_time,
            "duration_minutes": activity_duration_minutes,
            "transport_to": {
                "duration_minutes": travel_duration_minutes,
                "distance_km": travel_distance_km,
                "from_location": current_location.get("name", "起点")
            },
            "rating": poi.get("rating", 0),
            "phone_number": poi.get("phone_number", poi.get("tel", "")),
            "introduction": poi.get("introduction", f"{poi.get('name', '活动')}是一个值得游览的地方"),
            "image_urls": poi.get("image_urls", poi.get("photos", []))
        }

        # 4. 将活动添加到当天的行程中
        structured_itinerary = current_state.get("structured_itinerary", {})
        if current_day not in structured_itinerary:
            structured_itinerary[current_day] = []
        structured_itinerary[current_day].append(activity)

        # 5. 原子化更新时空状态
        new_current_time = end_time
        new_current_location = {
            "name": poi.get("name", "未知位置"),
            "lat": poi.get("lat", poi.get("latitude", 0)),
            "lon": poi.get("lon", poi.get("lng", poi.get("longitude", 0))),
            "address": poi.get("address", "")
        }

        # 如果POI有location字段（格式为"lng,lat"），解析坐标
        if poi.get("location") and "," in poi.get("location"):
            try:
                lng_str, lat_str = poi["location"].split(",", 1)
                new_current_location["lon"] = float(lng_str.strip())
                new_current_location["lat"] = float(lat_str.strip())
            except:
                logger.warning(f"POI位置解析失败：{poi.get('location')}")

        # 更新状态
        updated_icp_state = icp_state.copy()
        updated_icp_state.update({
            "current_time": new_current_time,
            "current_location": new_current_location
        })

        result = {
            "success": True,
            "activity": activity,
            "updated_icp_state": updated_icp_state,
            "updated_structured_itinerary": structured_itinerary,
            "travel_info": travel_info
        }

        logger.info(f"活动调度成功：{poi.get('name')} ({start_time}-{end_time})，新位置：{new_current_location.get('name')}")
        return result

    except Exception as e:
        logger.error(f"活动调度失败：{str(e)}")
        return {
            "success": False,
            "error": str(e),
            "activity": None
        }


@unified_registry.register_action_tool
def search_poi_by_name(
    poi_name: str,
    city: str,
    max_retries: int = 3
) -> List[Dict[str, Any]]:
    """
    P1.4 动态POI搜索工具

    赋予AI在规划中途搜索其初始POI池中不存在的特定地点的能力。
    复用tools/Amap/map_tool.py的search_pois方法。

    Args:
        poi_name: 要搜索的POI名称
        city: 城市名称
        max_retries: 最大重试次数

    Returns:
        格式化的POI列表
    """
    try:
        logger.info(f"搜索POI：{poi_name}，城市：{city}")

        map_tool = _get_map_tool()

        # 执行搜索（带重试逻辑）
        for attempt in range(max_retries):
            try:
                # 调用地图工具搜索
                poi_results = map_tool.search_pois(
                    keywords=poi_name,
                    city=city,
                    page_size=10,
                    sort_by="distance"
                )

                if poi_results:
                    # 格式化结果
                    formatted_pois = []
                    for poi_result in poi_results:
                        formatted_poi = {
                            "id": poi_result.id,
                            "poi_id": poi_result.id,
                            "name": poi_result.name,
                            "address": poi_result.address,
                            "poi_type": _determine_poi_type("", poi_result.type),
                            "location": f"{poi_result.location.longitude},{poi_result.location.latitude}",
                            "lat": poi_result.location.latitude,
                            "lon": poi_result.location.longitude,
                            "latitude": poi_result.location.latitude,
                            "longitude": poi_result.location.longitude,
                            "rating": poi_result.rating or 0,
                            "phone_number": poi_result.phone or "",
                            "tel": poi_result.phone or "",
                            "business_hours": poi_result.business_hours or "",
                            "price": poi_result.price,
                            "price_range": poi_result.price_range or "",
                            "photos": poi_result.photos or [],
                            "image_urls": poi_result.photos or [],
                            "image": poi_result.image,
                            "introduction": f"{poi_result.name}位于{poi_result.address}，是一个值得游览的地方",
                            "suggested_time": _generate_suggested_time(_determine_poi_type("", poi_result.type))
                        }
                        formatted_pois.append(formatted_poi)

                    logger.info(f"POI搜索成功，找到{len(formatted_pois)}个结果")
                    return formatted_pois
                else:
                    logger.warning(f"第{attempt + 1}次搜索返回空结果")
                    if attempt < max_retries - 1:
                        continue

            except Exception as search_error:
                logger.warning(f"第{attempt + 1}次搜索失败：{str(search_error)}")
                if attempt < max_retries - 1:
                    continue
                else:
                    raise search_error

        # 所有重试都失败
        logger.error(f"POI搜索失败，已重试{max_retries}次")
        return []

    except Exception as e:
        logger.error(f"POI搜索异常：{str(e)}")
        return []


def _generate_suggested_time(poi_type: str) -> str:
    """
    根据POI类型生成建议游览时间

    Args:
        poi_type: POI类型

    Returns:
        建议时间字符串
    """
    time_mapping = {
        "ATTRACTION": "上午 09:00 - 12:00",
        "RESTAURANT": "午餐 12:00 - 13:30",
        "HOTEL": "入住 15:00",
        "SHOPPING": "下午 14:00 - 17:00",
        "OTHER": "上午 10:00 - 11:30"
    }
    return time_mapping.get(poi_type, "上午 10:00 - 11:30")


@unified_registry.register_planner_tool
async def generate_planning_thought(
    current_state: Dict[str, Any],
    planning_context: Dict[str, Any]
) -> Dict[str, Any]:
    """
    **LLM驱动的规划思考工具 (V3.0 - P3阶段优化版)**

    基于当前状态和规划上下文，调用LLM生成下一步的思考和行动指令。

    P3阶段核心改进：
    1. 接收nearby_poi_options上下文信息
    2. 重构Prompt注入距离信息
    3. 引导LLM基于地理位置做决策

    Args:
        current_state: 当前规划状态（包含nearby_poi_options）
        planning_context: 规划上下文

    Returns:
        LLM返回的结构化思考和行动指令
    """
    try:
        # 1. 准备Prompt上下文
        icp_state = current_state.get("icp_planner_state", {})
        current_day = icp_state.get("current_day", 1)
        current_time = icp_state.get("current_time", "09:00")
        current_location = icp_state.get("current_location", {})

        # P3阶段核心改进：获取动态位置感知结果
        nearby_poi_options = current_state.get("nearby_poi_options", [])

        # 为了防止Prompt过长，对POI池进行采样（优先选择距离近的）
        remaining_pois = current_state.get("remaining_pois", [])
        if nearby_poi_options:
            # 使用距离排序后的POI，取前10个
            poi_pool_sample = nearby_poi_options[:10]
        else:
            # 回退到随机采样
            poi_pool_sample = random.sample(remaining_pois, min(len(remaining_pois), 10))

        # 获取当前天的已有活动
        daily_activities = current_state.get("daily_plans", {}).get(current_day, [])

        # 记录详细的调试信息
        logger.info(f"LLM思考上下文 - 第{current_day}天 {current_time}, 当前位置: {current_location.get('name', '未知')}, 已有活动: {len(daily_activities)}个, 附近POI: {len(nearby_poi_options)}个")

        # P3阶段核心改进：在上下文中包含距离信息
        context_for_prompt = {
            "user_preferences": current_state.get("consolidated_intent", {}).get("preferences", {}),
            "planning_state": {
                "current_day": current_day,
                "current_time": current_time,
                "current_location": current_location,
                "daily_activities": daily_activities,
                "nearby_poi_options": [
                    {
                        "name": p.get("name"),
                        "type": p.get("poi_type"),
                        "rating": p.get("rating"),
                        "address": p.get("address", ""),
                        "distance_km": p.get("distance_km", "未知"),
                        "location_priority": "高" if p.get("distance_km", float('inf')) < 5 else "中" if p.get("distance_km", float('inf')) < 15 else "低"
                    }
                    for p in poi_pool_sample
                ],
                "location_awareness": {
                    "total_nearby_options": len(nearby_poi_options),
                    "closest_poi": nearby_poi_options[0].get("name", "无") if nearby_poi_options else "无",
                    "closest_distance": nearby_poi_options[0].get("distance_km", "无") if nearby_poi_options else "无"
                }
            }
        }

        # 2. 加载并格式化Prompt
        formatted_prompt = get_travel_planner_prompt(
            "05_icp_step_planner",
            context_json=json.dumps(context_for_prompt, ensure_ascii=False, indent=2)
        )

        # 3. 调用轻量级LLM进行思考
        # 遵循分层模型策略，使用'basic'模型进行高频次的战术决策
        llm_manager = LLMManager()
        basic_llm_client = llm_manager.get_client("basic")
        
        response = await basic_llm_client.chat(formatted_prompt)
        response_text = response['content']

        # 4. 解析LLM返回的JSON
        # 移除Markdown代码块标记
        cleaned_json = response_text.strip().replace("```json", "").replace("```", "").strip()
        decision = json.loads(cleaned_json)
        
        logger.info(f"LLM a \n\n{decision.get('thought')}")

        return decision

    except Exception as e:
        logger.error(f"LLM驱动的思考生成失败: {str(e)}")
        # 返回一个安全的、默认的结束动作
        return {
            "thought": f"思考过程出现严重错误: {e}。为避免卡死，决定结束今天的规划。",
            "action": {
                "tool_name": "end_day_planning",
                "parameters": {}
            },
            "estimated_duration_minutes": 0
        }


@unified_registry.register_action_tool
def select_poi_from_pool(poi_name: str, current_state: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    从剩余POI池中按名称选择一个POI，并从池中移除以防重复选择。

    Args:
        poi_name: 要选择的POI的名称。
        current_state: 当前规划状态，包含remaining_pois。

    Returns:
        找到的POI字典，如果未找到则返回None。
    """
    remaining_pois = current_state.get("remaining_pois", [])
    used_poi_ids = current_state.get("used_poi_ids", [])

    for i, poi in enumerate(remaining_pois):
        if poi.get("name") == poi_name:
            # 检查是否已被使用
            poi_id = poi.get("id", poi.get("poi_id", ""))
            if poi_id in used_poi_ids:
                logger.warning(f"POI '{poi_name}' (ID: {poi_id}) 已被使用，跳过")
                continue

            # 从剩余池中移除并标记为已使用
            selected_poi = remaining_pois.pop(i)
            if poi_id:
                used_poi_ids.append(poi_id)

            logger.info(f"成功从POI池中选择了 '{poi_name}' (ID: {poi_id})，剩余POI: {len(remaining_pois)}个")
            return selected_poi

    logger.warning(f"无法在POI池中找到名为 '{poi_name}' 的POI")
    return None


@unified_registry.register_planner_tool
def select_next_action(
    thought_result: Dict[str, Any],
    available_tools: List[str],
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    选择下一步行动
    
    基于思考结果和可用工具，选择最合适的下一步行动
    
    Args:
        thought_result: 思考结果
        available_tools: 可用工具列表
        current_state: 当前状态
    
    Returns:
        行动选择结果
    """
    try:
        suggested_action = thought_result.get("next_action_suggestion", {})
        action_type = suggested_action.get("action_type", "search_poi")
        
        # 根据行动类型选择具体工具和参数
        if action_type == "search_poi":
            # 确定搜索参数
            daily_plans = current_state.get("daily_plans", {})
            current_day = len([day for day, plans in daily_plans.items() if plans]) + 1
            
            # 从consolidated_intent获取目的地信息
            consolidated_intent = current_state.get("consolidated_intent", {})
            destinations = consolidated_intent.get("destinations", ["北京"])
            current_destination = destinations[0] if destinations else "北京"
            
            # 从偏好分析获取景点类型
            preferences = consolidated_intent.get("preferences", {})
            attraction_prefs = preferences.get("attractions", {})
            preferred_types = attraction_prefs.get("preferred_types", ["历史文化"])
            
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": preferred_types[0] if preferred_types else "景点",
                    "city": current_destination,
                    "types": "景点",
                    "page_size": 10
                },
                "target_day": current_day,
                "expected_result": f"为第{current_day}天获取{preferred_types[0] if preferred_types else '景点'}列表"
            }
            
        elif action_type == "search_accommodation":
            consolidated_intent = current_state.get("consolidated_intent", {})
            destinations = consolidated_intent.get("destinations", ["北京"])
            
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": "酒店",
                    "city": destinations[0] if destinations else "北京",
                    "types": "住宿",
                    "page_size": 5
                },
                "expected_result": "获取住宿选项列表"
            }
            
        elif action_type == "finalize_itinerary":
            action = {
                "tool_name": "format_final_itinerary",
                "parameters": {
                    "daily_plans": current_state.get("daily_plans", {}),
                    "consolidated_intent": current_state.get("consolidated_intent", {})
                },
                "expected_result": "生成最终完整行程"
            }
            
        else:
            # 默认行动
            action = {
                "tool_name": "search_poi",
                "parameters": {
                    "keywords": "景点",
                    "city": "北京",
                    "page_size": 5
                },
                "expected_result": "获取景点信息"
            }
        
        return {
            "selected_action": action,
            "action_reasoning": suggested_action.get("reason", "基于当前状态选择的行动"),
            "confidence": thought_result.get("confidence", 0.8),
            "alternatives": []  # 可以添加备选行动
        }
        
    except Exception as e:
        logger.error(f"Failed to select next action: {str(e)}")
        return {
            "selected_action": {
                "tool_name": "search_poi",
                "parameters": {"keywords": "景点", "city": "北京"},
                "expected_result": "获取基础景点信息"
            },
            "action_reasoning": f"行动选择失败，使用默认行动: {str(e)}",
            "confidence": 0.1,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def observe_action_result(
    action: Dict[str, Any],
    action_result: Any,
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """
    观察行动结果
    
    分析工具执行结果，评估是否达到预期目标
    
    Args:
        action: 执行的行动
        action_result: 行动执行结果
        current_state: 当前状态
    
    Returns:
        观察结果
    """
    try:
        tool_name = action.get("tool_name", "unknown")
        expected_result = action.get("expected_result", "")
        
        # 分析结果质量
        if action_result is None:
            observation = {
                "success": False,
                "quality_score": 0.0,
                "observation": "工具执行失败，未获得结果",
                "next_step_suggestion": "重试或选择其他工具"
            }
        elif isinstance(action_result, list) and len(action_result) == 0:
            observation = {
                "success": False,
                "quality_score": 0.2,
                "observation": "工具执行成功但未找到相关结果",
                "next_step_suggestion": "调整搜索参数或选择其他工具"
            }
        elif isinstance(action_result, list) and len(action_result) > 0:
            # 分析POI搜索结果
            quality_score = min(1.0, len(action_result) / 5.0)  # 5个结果为满分
            
            observation = {
                "success": True,
                "quality_score": quality_score,
                "observation": f"成功获得{len(action_result)}个结果，质量评分{quality_score:.2f}",
                "result_summary": {
                    "count": len(action_result),
                    "sample_items": [item.get("name", "未知") for item in action_result[:3]]
                },
                "next_step_suggestion": "将结果添加到行程规划中" if quality_score > 0.6 else "考虑调整搜索条件"
            }
        else:
            # 其他类型结果
            observation = {
                "success": True,
                "quality_score": 0.7,
                "observation": f"获得{tool_name}执行结果",
                "result_summary": str(action_result)[:200],
                "next_step_suggestion": "继续下一步规划"
            }
        
        # 评估是否需要继续规划
        daily_plans = current_state.get("daily_plans", {})
        total_days = current_state.get("consolidated_intent", {}).get("travel_days", 3)
        completed_days = len([day for day, plans in daily_plans.items() if plans])
        
        should_continue = completed_days < total_days or not observation["success"]
        
        observation.update({
            "should_continue_planning": should_continue,
            "planning_progress": {
                "completed_days": completed_days,
                "total_days": total_days,
                "completion_rate": completed_days / total_days if total_days > 0 else 0
            }
        })
        
        return observation
        
    except Exception as e:
        logger.error(f"Failed to observe action result: {str(e)}")
        return {
            "success": False,
            "quality_score": 0.0,
            "observation": f"结果观察失败: {str(e)}",
            "should_continue_planning": True,
            "error": str(e)
        }


@unified_registry.register_planner_tool
def update_planning_state(
    current_state: Dict[str, Any],
    action: Dict[str, Any],
    action_result: Any,
    observation: Dict[str, Any]
) -> Dict[str, Any]:
    """
    更新规划状态
    
    基于行动结果和观察，更新规划状态
    
    Args:
        current_state: 当前状态
        action: 执行的行动
        action_result: 行动结果
        observation: 观察结果
    
    Returns:
        更新后的状态
    """
    try:
        updated_state = current_state.copy()
        
        # 更新工具结果缓存
        tool_results = updated_state.get("tool_results", {})
        tool_name = action.get("tool_name", "unknown")
        tool_results[tool_name] = action_result
        updated_state["tool_results"] = tool_results
        
        # 如果是POI搜索且成功，更新daily_plans（带主POI池管理和去重逻辑）
        if tool_name == "search_poi" and observation.get("success", False):
            target_day = action.get("target_day")
            if target_day and isinstance(action_result, list):
                daily_plans = updated_state.get("daily_plans", {})
                remaining_pois = updated_state.get("remaining_pois", [])
                used_poi_ids = set(updated_state.get("used_poi_ids", []))

                # 获取当前已有的POI列表
                existing_pois = daily_plans.get(target_day, [])
                existing_poi_ids = {poi.get("poi_id", "") for poi in existing_pois}
                existing_poi_names = {poi.get("name", "").lower() for poi in existing_pois}

                # 智能POI选择策略：优先从主POI池中选择，实现类型平衡
                new_pois = []
                pois_to_remove = []  # 需要从remaining_pois中移除的POI

                # 分析当前天已有的POI类型
                existing_poi_types = {}
                for existing_poi in existing_pois:
                    poi_type = existing_poi.get("poi_type", "ATTRACTION")
                    existing_poi_types[poi_type] = existing_poi_types.get(poi_type, 0) + 1

                # 如果有剩余POI池，优先从中智能选择
                if remaining_pois:
                    logger.info(f"从剩余POI池中智能选择POI，池中还有{len(remaining_pois)}个POI")
                    # 获取当前位置信息用于距离计算
                    current_location = updated_state.get("icp_planner_state", {}).get("current_location", {})
                    # 实现POI类型平衡选择，考虑距离因素
                    available_pois = _select_balanced_pois(remaining_pois, existing_poi_types, 5, current_location)
                    logger.info(f"智能选择了{len(available_pois)}个平衡的POI")
                else:
                    logger.info(f"剩余POI池为空，使用搜索结果")
                    available_pois = action_result[:5]  # 从搜索结果中取前5个

                for poi in available_pois:
                    # 使用真实的POI ID，而不是生成的序号
                    real_poi_id = poi.get("id", "")  # 使用高德API返回的真实ID
                    poi_name = poi.get("name", "未知景点")
                    poi_name_lower = poi_name.lower()

                    # 检查是否已被使用（通过真实ID和名称双重检查）
                    if (real_poi_id and real_poi_id in used_poi_ids) or (real_poi_id and real_poi_id in existing_poi_ids) or (poi_name_lower in existing_poi_names):
                        logger.info(f"跳过已使用的POI: {poi_name} (真实ID: {real_poi_id})")
                        continue

                    # 如果没有真实ID，生成一个唯一ID（包含名称和地址的哈希）
                    if not real_poi_id:
                        import hashlib
                        unique_str = f"{poi_name}_{poi.get('address', '')}_{poi.get('location', '')}"
                        real_poi_id = hashlib.md5(unique_str.encode()).hexdigest()[:12]
                        logger.info(f"为POI生成唯一ID: {poi_name} -> {real_poi_id}")

                    # 创建新POI条目
                    new_poi = {
                        "poi_id": real_poi_id,
                        "name": poi_name,
                        "address": poi.get("address", ""),
                        "poi_type": _determine_poi_type(poi.get("typecode", ""), poi.get("type", "")),
                        "location": poi.get("location", ""),
                        "rating": poi.get("rating", 0),
                        "phone_number": poi.get("tel", ""),
                        "introduction": poi.get("description", f"{poi_name}是一个值得游览的地方"),
                        "suggested_time": _generate_suggested_time(_determine_poi_type(poi.get("typecode", ""), poi.get("type", ""))),
                        "image_urls": poi.get("photos", [])
                    }

                    new_pois.append(new_poi)
                    existing_poi_names.add(poi_name_lower)
                    existing_poi_ids.add(new_poi["poi_id"])

                    # 标记为已使用
                    if real_poi_id:
                        used_poi_ids.add(real_poi_id)
                        # 找到对应的POI并标记为需要移除
                        for remaining_poi in remaining_pois:
                            if remaining_poi.get("id") == real_poi_id:
                                pois_to_remove.append(remaining_poi)
                                break

                    # 限制每天最多3个新POI
                    if len(new_pois) >= 3:
                        break

                # 从remaining_pois中移除已使用的POI
                for poi_to_remove in pois_to_remove:
                    if poi_to_remove in remaining_pois:
                        remaining_pois.remove(poi_to_remove)
                        logger.info(f"从剩余POI池中移除: {poi_to_remove.get('name', 'Unknown')}")

                # 合并现有POI和新POI
                if new_pois:
                    daily_plans[target_day] = existing_pois + new_pois
                    updated_state["daily_plans"] = daily_plans
                    updated_state["remaining_pois"] = remaining_pois
                    updated_state["used_poi_ids"] = list(used_poi_ids)  # 转换为list以支持JSON序列化
                    logger.info(f"为第{target_day}天添加了{len(new_pois)}个新POI，总计{len(daily_plans[target_day])}个POI")
                    logger.info(f"剩余POI池还有{len(remaining_pois)}个POI，已使用{len(used_poi_ids)}个POI")
                else:
                    logger.warning(f"第{target_day}天没有找到新的POI，可能都已存在或POI池已耗尽")
        
        # 更新规划日志
        planning_log = updated_state.get("planning_log", [])
        log_entry = f"执行{tool_name}，结果：{observation.get('observation', '未知')}"
        planning_log.append(log_entry)
        updated_state["planning_log"] = planning_log
        
        # 更新当前行动
        updated_state["current_action"] = action
        
        return updated_state
        
    except Exception as e:
        logger.error(f"Failed to update planning state: {str(e)}")
        return current_state


@unified_registry.register_planner_tool
def check_planning_completion(
    current_state: Dict[str, Any],
    planning_context: Dict[str, Any]
) -> Dict[str, Any]:
    """
    检查规划完成情况
    
    评估当前规划是否已完成或需要继续
    
    Args:
        current_state: 当前状态
        planning_context: 规划上下文
    
    Returns:
        完成情况检查结果
    """
    try:
        daily_plans = current_state.get("daily_plans", {})
        total_days = planning_context.get("constraints", {}).get("max_days", 3)
        
        # 检查每天是否都有规划
        completed_days = 0
        for day in range(1, total_days + 1):
            if day in daily_plans and daily_plans[day]:
                completed_days += 1
        
        completion_rate = completed_days / total_days if total_days > 0 else 0
        is_complete = completion_rate >= 1.0
        
        # 检查规划质量
        quality_issues = []
        if completed_days < total_days:
            quality_issues.append(f"还有{total_days - completed_days}天未规划")
        
        # 检查预算
        budget_used = current_state.get("total_budget_tracker", 0)
        budget_limit = planning_context.get("constraints", {}).get("budget_limit", 1000)
        if budget_used > budget_limit:
            quality_issues.append(f"预算超支{budget_used - budget_limit}元")
        
        return {
            "is_complete": is_complete,
            "completion_rate": completion_rate,
            "completed_days": completed_days,
            "total_days": total_days,
            "quality_score": 1.0 - len(quality_issues) * 0.2,
            "quality_issues": quality_issues,
            "recommendation": "规划完成" if is_complete and not quality_issues else "需要继续规划或优化"
        }
        
    except Exception as e:
        logger.error(f"Failed to check planning completion: {str(e)}")
        return {
            "is_complete": False,
            "completion_rate": 0.0,
            "quality_score": 0.0,
            "error": str(e),
            "recommendation": "检查失败，建议重新评估"
        }


@unified_registry.register_action_tool
async def optimize_daily_route(pois: List[Dict[str, Any]], trace_id: str = "") -> List[Dict[str, Any]]:
    """
    优化当天的POI访问顺序，基于地理位置进行排序以减少路线上的"反复横跳"

    Args:
        pois: POI列表，每个POI应包含location字段（格式为"lng,lat"）
        trace_id: 追踪ID

    Returns:
        优化后的POI列表
    """
    try:
        logger.info(f"[{trace_id}] 开始优化每日路线，POI数量: {len(pois)}")

        if len(pois) <= 1:
            logger.info(f"[{trace_id}] POI数量不足，无需优化")
            return pois

        # 提取有效位置的POI
        pois_with_location = []
        pois_without_location = []

        for poi in pois:
            location = poi.get('location', '').strip()
            if location and ',' in location:
                try:
                    lng_str, lat_str = location.split(',', 1)
                    lng, lat = float(lng_str.strip()), float(lat_str.strip())

                    # 验证经纬度范围
                    if -180 <= lng <= 180 and -90 <= lat <= 90:
                        poi_copy = poi.copy()
                        poi_copy['_lng'] = lng
                        poi_copy['_lat'] = lat
                        pois_with_location.append(poi_copy)
                    else:
                        logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 经纬度超出范围: {location}")
                        pois_without_location.append(poi)
                except (ValueError, IndexError) as e:
                    logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 位置解析失败: {location}, 错误: {str(e)}")
                    pois_without_location.append(poi)
            else:
                logger.warning(f"[{trace_id}] POI {poi.get('name', 'Unknown')} 缺少有效位置信息")
                pois_without_location.append(poi)

        if not pois_with_location:
            logger.warning(f"[{trace_id}] 没有有效位置的POI，返回原始顺序")
            return pois

        # 实现简单的地理排序算法
        # 按经度排序，然后按纬度排序，实现从西到东、从南到北的游览顺序
        pois_with_location.sort(key=lambda x: (x.get('_lng', 0), x.get('_lat', 0)))

        # 移除临时添加的经纬度字段
        for poi in pois_with_location:
            poi.pop('_lng', None)
            poi.pop('_lat', None)

        # 将有位置的POI和无位置的POI合并（无位置的放在最后）
        optimized_pois = pois_with_location + pois_without_location

        logger.info(f"[{trace_id}] 路线优化完成，有效位置POI: {len(pois_with_location)}, 无位置POI: {len(pois_without_location)}")

        return optimized_pois

    except Exception as e:
        logger.error(f"[{trace_id}] 路线优化失败: {str(e)}")
        return pois  # 返回原始顺序


@unified_registry.register_action_tool
async def calculate_route_distance(pois: List[Dict[str, Any]], trace_id: str = "") -> Dict[str, Any]:
    """
    计算POI列表的总路线距离和时间

    Args:
        pois: POI列表
        trace_id: 追踪ID

    Returns:
        路线统计信息
    """
    try:
        logger.info(f"[{trace_id}] 开始计算路线距离，POI数量: {len(pois)}")

        if len(pois) < 2:
            return {
                "total_distance": 0.0,
                "total_duration": 0,
                "route_segments": [],
                "average_distance": 0.0
            }

        total_distance = 0.0  # 公里
        total_duration = 0    # 分钟
        route_segments = []

        for i in range(len(pois) - 1):
            current_poi = pois[i]
            next_poi = pois[i + 1]

            current_location = current_poi.get('location', '')
            next_location = next_poi.get('location', '')

            if current_location and next_location:
                # 计算直线距离（简化版本）
                distance = _calculate_haversine_distance(current_location, next_location)
                # 估算步行时间（假设步行速度4km/h）
                duration = int(distance * 15)  # 分钟

                segment = {
                    "from": current_poi.get('name', 'Unknown'),
                    "to": next_poi.get('name', 'Unknown'),
                    "distance": round(distance, 2),
                    "duration": duration
                }
                route_segments.append(segment)

                total_distance += distance
                total_duration += duration

        average_distance = total_distance / len(route_segments) if route_segments else 0.0

        result = {
            "total_distance": round(total_distance, 2),
            "total_duration": total_duration,
            "route_segments": route_segments,
            "average_distance": round(average_distance, 2)
        }

        logger.info(f"[{trace_id}] 路线计算完成，总距离: {result['total_distance']}km, 总时间: {result['total_duration']}分钟")

        return result

    except Exception as e:
        logger.error(f"[{trace_id}] 路线距离计算失败: {str(e)}")
        return {
            "total_distance": 0.0,
            "total_duration": 0,
            "route_segments": [],
            "average_distance": 0.0
        }


def _calculate_haversine_distance(location1: str, location2: str) -> float:
    """
    使用Haversine公式计算两个经纬度点之间的直线距离

    Args:
        location1: 位置1，格式为"lng,lat"
        location2: 位置2，格式为"lng,lat"

    Returns:
        距离（公里）
    """
    if not location1 or not location2 or "," not in location1 or "," not in location2:
        return float('inf') # 返回无穷大，使无效位置的POI排序在最后

    try:
        lng1, lat1 = map(float, location1.split(','))
        lng2, lat2 = map(float, location2.split(','))

        # 转换为弧度
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])

        # Haversine公式
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # 地球半径（公里）
        r = 6371

        return c * r

    except (ValueError, IndexError):
        return 0.0


def _determine_poi_type(typecode: str, type_name: str) -> str:
    """
    根据高德API的typecode和type字段确定POI类型

    Args:
        typecode: 高德API返回的类型编码
        type_name: 高德API返回的类型名称

    Returns:
        标准化的POI类型: ATTRACTION, RESTAURANT, HOTEL
    """
    # 高德地图POI类型编码映射
    if typecode:
        # 餐饮服务类型编码以05开头
        if typecode.startswith("05"):
            return "RESTAURANT"
        # 住宿服务类型编码以10开头
        elif typecode.startswith("10"):
            return "HOTEL"
        # 风景名胜类型编码以11开头
        elif typecode.startswith("11"):
            return "ATTRACTION"

    # 如果typecode不明确，根据type_name判断
    if type_name:
        type_lower = type_name.lower()
        if any(keyword in type_lower for keyword in ["餐厅", "饭店", "美食", "小吃", "咖啡", "茶楼"]):
            return "RESTAURANT"
        elif any(keyword in type_lower for keyword in ["酒店", "宾馆", "旅馆", "客栈", "民宿"]):
            return "HOTEL"
        elif any(keyword in type_lower for keyword in ["景点", "公园", "博物馆", "寺庙", "古迹", "广场"]):
            return "ATTRACTION"

    # 默认返回景点类型
    return "ATTRACTION"


def _select_balanced_pois(remaining_pois: list, existing_poi_types: dict, max_count: int, current_location: dict = None) -> list:
    """
    从剩余POI池中智能选择平衡的POI，考虑距离和类型平衡

    Args:
        remaining_pois: 剩余POI池
        existing_poi_types: 当前已有的POI类型统计
        max_count: 最大选择数量
        current_location: 当前位置（用于距离计算）

    Returns:
        选择的POI列表
    """
    import random
    import math

    if not remaining_pois:
        return []

    # 按POI类型分组
    pois_by_type = {
        "ATTRACTION": [],
        "RESTAURANT": [],
        "HOTEL": []
    }

    for poi in remaining_pois:
        poi_type = _determine_poi_type(poi.get("typecode", ""), poi.get("type", ""))
        if poi_type in pois_by_type:
            pois_by_type[poi_type].append(poi)

    selected_pois = []

    # 优先选择缺少的类型
    attraction_count = existing_poi_types.get("ATTRACTION", 0)
    restaurant_count = existing_poi_types.get("RESTAURANT", 0)

    # 每天理想的POI组合：2个景点 + 1个餐厅
    target_attractions = max(0, 2 - attraction_count)
    target_restaurants = max(0, 1 - restaurant_count)

    # 距离感知选择函数
    def select_by_distance_and_rating(poi_list: list, count: int) -> list:
        if not poi_list or count <= 0:
            return []

        # 如果有当前位置，按距离排序；否则按评分排序
        if current_location and current_location.get("lon") and current_location.get("lat"):
            current_loc_str = f"{current_location['lon']},{current_location['lat']}"
            
            # 计算距离并排序
            poi_with_distance = []
            for poi in poi_list:
                distance = _calculate_haversine_distance(current_loc_str, poi.get("location"))
                rating = float(poi.get("rating", 0))

                # 综合评分：距离越近越好（距离无穷大时评分为负无穷），评分越高越好
                # 避免除以零
                score = rating - (distance / 10 if distance > 0 else 0)
                poi_with_distance.append((poi, score))

            # 按综合评分排序
            poi_with_distance.sort(key=lambda x: x[1], reverse=True)
            return [poi for poi, _ in poi_with_distance[:count]]
        else:
            # 按评分排序
            sorted_pois = sorted(poi_list, key=lambda x: float(x.get("rating", 0)), reverse=True)
            return sorted_pois[:count]

    # 智能选择景点（优先距离近且评分高的）
    if target_attractions > 0 and pois_by_type["ATTRACTION"]:
        attractions = select_by_distance_and_rating(pois_by_type["ATTRACTION"], target_attractions)
        selected_pois.extend(attractions)

    # 智能选择餐厅
    if target_restaurants > 0 and pois_by_type["RESTAURANT"]:
        restaurants = select_by_distance_and_rating(pois_by_type["RESTAURANT"], target_restaurants)
        selected_pois.extend(restaurants)

    # 如果还没达到最大数量，智能选择剩余的POI
    if len(selected_pois) < max_count:
        remaining_count = max_count - len(selected_pois)
        used_ids = {poi.get("id") for poi in selected_pois}

        available_remaining = [
            poi for poi in remaining_pois
            if poi.get("id") not in used_ids
        ]

        if available_remaining:
            additional_pois = select_by_distance_and_rating(available_remaining, remaining_count)
            selected_pois.extend(additional_pois)

    logger.info(f"智能POI选择完成：选择了{len(selected_pois)}个POI，类型分布：{[poi.get('poi_type', 'UNKNOWN') for poi in selected_pois]}")
    return selected_pois[:max_count]
